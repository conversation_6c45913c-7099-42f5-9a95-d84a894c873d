import os
import json
# from datetime import datetime
# from langchain_community.document_loaders import DirectoryLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings
from langchain_community.llms import Ollama
from langchain.chains import RetrievalQA
from langchain.prompts import PromptTemplate

class HarborRAGSystem:
    def __init__(self, model_name="llama3.2"):
        # 初始化配置
        self.data_path = r"C:\Users\<USER>\新建文件夹 (13)"
        self.model = Ollama(model=model_name)
        self.embeddings = OllamaEmbeddings(model=model_name)
        
        # RAG提示词模板
        self.rag_template = """作为港航数据专家，请根据以下知识库内容回答问题：
        上下文：data:{context}
        每一条{context}中包含一条船舶的JSON数据
        从{context}中解析JSON数据并提取以下结构化信息：
            观测点卡口的ID号:gcdid
            船舶的名称:cmch
            船舶的AIS识别号:aissbh
            船舶通过观测点卡口的时间:tgsj
        问题：{question}
        回答要求：
        1. 必须基于上下文内容
        2. 数据需标注来源文件
        3. 技术解释保持专业
        4. 超出知识范围明确说明
        """
        
        self.rag_prompt = PromptTemplate(
            template=self.rag_template,
            input_variables=["context", "question"]
        )
        
        # 构建知识库
        self.vectorstore = self._build_knowledge_base()
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.model,
            chain_type="stuff",
            retriever=self.vectorstore.as_retriever(
                search_type="similarity",
                search_kwargs={"k": 2}
            ),
            return_source_documents=True,
            chain_type_kwargs={
                "prompt": self.rag_prompt,
                "document_variable_name": "context"
            }
        )
    
    def _load_json_files(self):
        """加载JSON格式的数据文件"""
        json_files = []
        for root, _, files in os.walk(self.data_path):
            for file in files:
                if file.endswith('.json'):
                    full_path = os.path.join(root, file)
                    with open(full_path, 'r', encoding='utf-8') as f:
                        try:
                            data = json.load(f)
                            json_files.append({
                                'file_path': full_path,
                                'content': json.dumps(data, ensure_ascii=False)
                            })
                        except json.JSONDecodeError:
                            print(f"警告: 文件 {file} 解析失败")
        return json_files
    
    def _build_knowledge_base(self):
        """构建向量知识库"""
        documents = self._load_json_files()
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100
        )
        splits = text_splitter.create_documents(
            [doc['content'] for doc in documents],
            metadatas=[{'source': doc['file_path']} for doc in documents]
        )
        return FAISS.from_documents(splits, self.embeddings)
    
    def query(self, question):
        """执行RAG查询"""
        result = self.qa_chain({
            "query": question
        })
        
        return {
            "answer": result["result"],
            "sources": list(set([doc.metadata['source'] for doc in result['source_documents']]))
        }

if __name__ == "__main__":
    rag_system = HarborRAGSystem()
    print("系统已启动(输入q退出)")
    while True:
        user_input = input("\n请输入查询问题: ")
        if user_input.lower() == 'q':
            break
            
        response = rag_system.query(user_input)
        print(f"\n回答：{response['answer']}")
        if response['sources']:
            print("\n数据来源：")
            for source in response['sources']:
                print(f"- {os.path.basename(source)}")
